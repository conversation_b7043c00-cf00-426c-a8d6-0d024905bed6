'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/components/providers'
import { formatCurrency, getAgeGroup, calculateEndDate, isSportSafeForPregnancy } from '@/lib/utils'
import { supabase } from '@/lib/supabase'
import { useToast } from '@/hooks/use-toast'
import {
  UserPlus,
  ArrowLeft,
  Save,
  AlertTriangle,
  Info,
} from 'lucide-react'

interface SportsPricing {
  id: string
  sport: string
  gender: 'male' | 'female' | 'both'
  age_group: 'child' | 'adult' | 'senior' | 'all'
  monthly_price: number
  quarterly_price: number
  yearly_price: number
  pregnancy_allowed: boolean
}

export default function NewMemberPage() {
  const [formData, setFormData] = useState({
    full_name: '',
    gender: 'male' as 'male' | 'female',
    age: '',
    phone: '',
    email: '',
    pregnant: false,
    sport: '',
    plan_type: 'monthly' as 'monthly' | 'quarterly' | 'yearly',
  })
  const [sportsPricing, setSportsPricing] = useState<SportsPricing[]>([])
  const [availableSports, setAvailableSports] = useState<SportsPricing[]>([])
  const [selectedSportPricing, setSelectedSportPricing] = useState<SportsPricing | null>(null)
  const [loading, setLoading] = useState(false)
  const [loadingPricing, setLoadingPricing] = useState(true)
  const router = useRouter()
  const { t } = useLanguage()
  const { toast } = useToast()

  useEffect(() => {
    fetchSportsPricing()
  }, [])

  useEffect(() => {
    filterAvailableSports()
  }, [sportsPricing, formData.gender, formData.age, formData.pregnant])

  useEffect(() => {
    updateSelectedSportPricing()
  }, [formData.sport, availableSports])

  const fetchSportsPricing = async () => {
    try {
      const { data, error } = await supabase
        .from('sports_pricing')
        .select('*')
        .order('sport')

      if (error) throw error
      setSportsPricing(data || [])
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load sports pricing',
        variant: 'destructive',
      })
    } finally {
      setLoadingPricing(false)
    }
  }

  const filterAvailableSports = () => {
    if (!formData.age) {
      setAvailableSports([])
      return
    }

    const ageGroup = getAgeGroup(parseInt(formData.age))
    
    const filtered = sportsPricing.filter(pricing => {
      // Check gender compatibility
      const genderMatch = pricing.gender === 'both' || pricing.gender === formData.gender
      
      // Check age group compatibility
      const ageMatch = pricing.age_group === 'all' || pricing.age_group === ageGroup
      
      // Check pregnancy safety
      const pregnancyMatch = !formData.pregnant || pricing.pregnancy_allowed
      
      return genderMatch && ageMatch && pregnancyMatch
    })

    setAvailableSports(filtered)
  }

  const updateSelectedSportPricing = () => {
    if (!formData.sport) {
      setSelectedSportPricing(null)
      return
    }

    const pricing = availableSports.find(p => p.sport === formData.sport)
    setSelectedSportPricing(pricing || null)
  }

  const getPrice = () => {
    if (!selectedSportPricing) return 0
    
    switch (formData.plan_type) {
      case 'monthly':
        return selectedSportPricing.monthly_price
      case 'quarterly':
        return selectedSportPricing.quarterly_price
      case 'yearly':
        return selectedSportPricing.yearly_price
      default:
        return 0
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!selectedSportPricing) {
      toast({
        title: 'Error',
        description: 'Please select a sport',
        variant: 'destructive',
      })
      return
    }

    setLoading(true)

    try {
      // Create user
      const { data: user, error: userError } = await supabase
        .from('users')
        .insert({
          full_name: formData.full_name,
          gender: formData.gender,
          age: parseInt(formData.age),
          phone: formData.phone,
          email: formData.email || null,
          pregnant: formData.gender === 'female' ? formData.pregnant : false,
        })
        .select()
        .single()

      if (userError) throw userError

      // Create subscription
      const startDate = new Date().toISOString().split('T')[0]
      const endDate = calculateEndDate(startDate, formData.plan_type)

      const { error: subscriptionError } = await supabase
        .from('subscriptions')
        .insert({
          user_id: user.id,
          sport: formData.sport,
          plan_type: formData.plan_type,
          start_date: startDate,
          end_date: endDate,
          price_dzd: getPrice(),
        })

      if (subscriptionError) throw subscriptionError

      toast({
        title: 'Member Added',
        description: `${formData.full_name} has been successfully registered`,
      })

      router.push('/members')
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to add member',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  if (loadingPricing) {
    return (
      <MainLayout title="Add New Member">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout title="Add New Member">
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Add New Member
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Register a new gym member with subscription
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Personal Information */}
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>
                Basic member details and contact information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Full Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  required
                  value={formData.full_name}
                  onChange={(e) => handleInputChange('full_name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  placeholder="Enter full name"
                />
              </div>

              {/* Gender and Age */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Gender *
                  </label>
                  <select
                    required
                    value={formData.gender}
                    onChange={(e) => handleInputChange('gender', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  >
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Age *
                  </label>
                  <input
                    type="number"
                    required
                    min="1"
                    max="120"
                    value={formData.age}
                    onChange={(e) => handleInputChange('age', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Enter age"
                  />
                </div>
              </div>

              {/* Phone and Email */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    required
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="0555123456"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email (Optional)
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              {/* Pregnancy Status (for females) */}
              {formData.gender === 'female' && (
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="pregnant"
                    checked={formData.pregnant}
                    onChange={(e) => handleInputChange('pregnant', e.target.checked)}
                    className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500 dark:focus:ring-red-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <label htmlFor="pregnant" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Currently pregnant
                  </label>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Subscription Information */}
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle>Subscription Details</CardTitle>
              <CardDescription>
                Choose sport and subscription plan
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Sport Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Sport *
                </label>
                <select
                  required
                  value={formData.sport}
                  onChange={(e) => handleInputChange('sport', e.target.value)}
                  disabled={!formData.age}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent disabled:opacity-50"
                >
                  <option value="">Select a sport</option>
                  {availableSports.map((pricing) => (
                    <option key={pricing.id} value={pricing.sport}>
                      {pricing.sport}
                    </option>
                  ))}
                </select>
                {!formData.age && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Please enter age first to see available sports
                  </p>
                )}
                {formData.pregnant && formData.sport && !isSportSafeForPregnancy(formData.sport) && (
                  <div className="flex items-center space-x-2 mt-2 p-2 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <AlertTriangle className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                    <p className="text-sm text-orange-600 dark:text-orange-400">
                      This sport may not be suitable during pregnancy. Please consult with a doctor.
                    </p>
                  </div>
                )}
              </div>

              {/* Plan Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Subscription Plan *
                </label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {['monthly', 'quarterly', 'yearly'].map((plan) => (
                    <div
                      key={plan}
                      className={`p-3 border rounded-lg cursor-pointer transition-all ${
                        formData.plan_type === plan
                          ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                          : 'border-gray-300 dark:border-gray-600 hover:border-red-300'
                      }`}
                      onClick={() => handleInputChange('plan_type', plan)}
                    >
                      <div className="text-center">
                        <p className="font-medium text-gray-900 dark:text-white capitalize">
                          {plan}
                        </p>
                        {selectedSportPricing && (
                          <p className="text-sm text-red-600 dark:text-red-400 font-semibold">
                            {formatCurrency(
                              plan === 'monthly' ? selectedSportPricing.monthly_price :
                              plan === 'quarterly' ? selectedSportPricing.quarterly_price :
                              selectedSportPricing.yearly_price
                            )}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Price Summary */}
              {selectedSportPricing && (
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                  <div className="flex justify-between items-center">
                    <span className="font-medium text-gray-900 dark:text-white">
                      Total Amount:
                    </span>
                    <span className="text-xl font-bold text-red-600 dark:text-red-400">
                      {formatCurrency(getPrice())}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2 mt-2">
                    <Info className="w-4 h-4 text-blue-500" />
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Subscription will start today and end on{' '}
                      {calculateEndDate(new Date().toISOString().split('T')[0], formData.plan_type)}
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="gym"
              disabled={loading || !selectedSportPricing}
            >
              {loading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Adding Member...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Add Member
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </MainLayout>
  )
}
