'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON>, useParams } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/components/providers'
import { formatCurrency, getAgeGroup, calculateEndDate } from '@/lib/utils'
import { supabase } from '@/lib/supabase'
import { useToast } from '@/hooks/use-toast'
import {
  ArrowLeft,
  Save,
  AlertTriangle,
  Info,
  User,
  Calendar,
} from 'lucide-react'

interface Member {
  id: string
  full_name: string
  gender: 'male' | 'female'
  age: number
  phone: string
  email?: string
  pregnant?: boolean
  situation: string
  remarks?: string
  created_at: string
  subscriptions?: Subscription[]
}

interface Subscription {
  id: string
  sport: string
  plan_type: 'monthly' | 'quarterly' | 'yearly'
  start_date: string
  end_date: string
  price_dzd: number
  status: 'active' | 'expiring' | 'expired'
}

interface SportsPricing {
  id: string
  sport: string
  gender: 'male' | 'female' | 'both'
  age_group: 'child' | 'adult' | 'senior' | 'all'
  monthly_price: number
  quarterly_price: number
  yearly_price: number
  pregnancy_allowed: boolean
}

export default function EditMemberPage() {
  const [member, setMember] = useState<Member | null>(null)
  const [formData, setFormData] = useState({
    full_name: '',
    gender: 'male' as 'male' | 'female',
    age: '',
    phone: '',
    email: '',
    pregnant: false,
    situation: 'active',
    remarks: '',
  })
  const [sportsPricing, setSportsPricing] = useState<SportsPricing[]>([])
  const [loading, setLoading] = useState(false)
  const [loadingMember, setLoadingMember] = useState(true)
  const [loadingPricing, setLoadingPricing] = useState(true)
  const router = useRouter()
  const params = useParams()
  const { t } = useLanguage()
  const { toast } = useToast()

  useEffect(() => {
    if (params.id) {
      fetchMember()
      fetchSportsPricing()
    }
  }, [params.id])

  useEffect(() => {
    if (member) {
      setFormData({
        full_name: member.full_name,
        gender: member.gender,
        age: member.age.toString(),
        phone: member.phone,
        email: member.email || '',
        pregnant: member.pregnant || false,
        situation: member.situation,
        remarks: member.remarks || '',
      })
    }
  }, [member])

  const fetchMember = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select(`
          *,
          subscriptions (*)
        `)
        .eq('id', params.id)
        .single()

      if (error) throw error
      setMember(data)
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to load member',
        variant: 'destructive',
      })
      router.push('/members')
    } finally {
      setLoadingMember(false)
    }
  }

  const fetchSportsPricing = async () => {
    try {
      const { data, error } = await supabase
        .from('sports_pricing')
        .select('*')
        .order('sport')

      if (error) throw error
      setSportsPricing(data || [])
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load sports pricing',
        variant: 'destructive',
      })
    } finally {
      setLoadingPricing(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const { error } = await supabase
        .from('users')
        .update({
          full_name: formData.full_name,
          gender: formData.gender,
          age: parseInt(formData.age),
          phone: formData.phone,
          email: formData.email || null,
          pregnant: formData.gender === 'female' ? formData.pregnant : false,
          situation: formData.situation,
          remarks: formData.remarks || null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', params.id)

      if (error) throw error

      toast({
        title: t('member_updated'),
        description: `${formData.full_name} has been successfully updated`,
      })

      router.push('/members')
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update member',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getActiveSubscription = () => {
    if (!member?.subscriptions) return null
    return member.subscriptions
      .filter(sub => sub.status === 'active')
      .sort((a, b) => new Date(b.end_date).getTime() - new Date(a.end_date).getTime())[0] || null
  }

  if (loadingMember || loadingPricing) {
    return (
      <MainLayout title={t('edit_member')}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
        </div>
      </MainLayout>
    )
  }

  if (!member) {
    return (
      <MainLayout title={t('edit_member')}>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Member not found
          </h2>
          <Button variant="gym" onClick={() => router.push('/members')} className="mt-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Members
          </Button>
        </div>
      </MainLayout>
    )
  }

  const activeSub = getActiveSubscription()

  return (
    <MainLayout title={t('edit_member')}>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('edit_member')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Update member information and details
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Personal Information */}
            <div className="lg:col-span-2">
              <Card className="glass border-white/20">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <User className="w-5 h-5" />
                    <span>{t('personal_information')}</span>
                  </CardTitle>
                  <CardDescription>
                    Update basic member information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Full Name */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('full_name')} *
                      </label>
                      <input
                        type="text"
                        required
                        value={formData.full_name}
                        onChange={(e) => handleInputChange('full_name', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      />
                    </div>

                    {/* Gender */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('gender')} *
                      </label>
                      <select
                        required
                        value={formData.gender}
                        onChange={(e) => handleInputChange('gender', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      >
                        <option value="male">{t('male')}</option>
                        <option value="female">{t('female')}</option>
                      </select>
                    </div>

                    {/* Age */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('age')} *
                      </label>
                      <input
                        type="number"
                        required
                        min="1"
                        max="120"
                        value={formData.age}
                        onChange={(e) => handleInputChange('age', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      />
                    </div>

                    {/* Phone */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('phone')} *
                      </label>
                      <input
                        type="tel"
                        required
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      />
                    </div>

                    {/* Email */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('email')}
                      </label>
                      <input
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      />
                    </div>

                    {/* Situation */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('situation')}
                      </label>
                      <select
                        value={formData.situation}
                        onChange={(e) => handleInputChange('situation', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      >
                        <option value="active">{t('active')}</option>
                        <option value="pregnant">Pregnant</option>
                        <option value="sick">Sick</option>
                        <option value="injured">Injured</option>
                        <option value="vacation">Vacation</option>
                      </select>
                    </div>
                  </div>

                  {/* Pregnancy checkbox for females */}
                  {formData.gender === 'female' && (
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="pregnant"
                        checked={formData.pregnant}
                        onChange={(e) => handleInputChange('pregnant', e.target.checked)}
                        className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                      />
                      <label htmlFor="pregnant" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {t('pregnant')}
                      </label>
                    </div>
                  )}

                  {/* Remarks */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {t('remarks')}
                    </label>
                    <textarea
                      value={formData.remarks}
                      onChange={(e) => handleInputChange('remarks', e.target.value)}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      placeholder="Additional notes or remarks"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Current Subscription Info */}
            <div>
              <Card className="glass border-white/20">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Calendar className="w-5 h-5" />
                    <span>Current Subscription</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {activeSub ? (
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Sport</p>
                        <p className="font-medium text-gray-900 dark:text-white">{activeSub.sport}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Plan</p>
                        <p className="font-medium text-gray-900 dark:text-white capitalize">{activeSub.plan_type}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Price</p>
                        <p className="font-medium text-gray-900 dark:text-white">{formatCurrency(activeSub.price_dzd)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">End Date</p>
                        <p className="font-medium text-gray-900 dark:text-white">{activeSub.end_date}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Status</p>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          activeSub.status === 'active' ? 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20' :
                          activeSub.status === 'expiring' ? 'text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/20' :
                          'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20'
                        }`}>
                          {activeSub.status.charAt(0).toUpperCase() + activeSub.status.slice(1)}
                        </span>
                      </div>
                      <Button variant="gym" size="sm" className="w-full mt-4" asChild>
                        <a href={`/members/${member.id}/subscription`}>
                          <Calendar className="w-3 h-3 mr-1" />
                          Renew Subscription
                        </a>
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-gray-600 dark:text-gray-400 text-sm">
                        No active subscription
                      </p>
                      <Button variant="gym" size="sm" className="mt-2" asChild>
                        <a href={`/members/${member.id}/subscription`}>
                          Add Subscription
                        </a>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={loading}
            >
              {t('cancel')}
            </Button>
            <Button
              type="submit"
              variant="gym"
              disabled={loading}
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {t('save')}
            </Button>
          </div>
        </form>
      </div>
    </MainLayout>
  )
}
